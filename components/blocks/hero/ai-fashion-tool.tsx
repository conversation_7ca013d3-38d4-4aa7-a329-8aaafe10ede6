"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, Download, Share, MoreHorizontal, Image as ImageIcon, Shirt, Wand2, Loader2 } from "lucide-react";

export default function AIFashionTool() {
  const [referenceImage, setReferenceImage] = useState<string | null>(null);
  const [garmentImage, setGarmentImage] = useState<string | null>(null);
  const [styleIntensity, setStyleIntensity] = useState(75);
  const [quality, setQuality] = useState("high");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [selectedHistoryItem, setSelectedHistoryItem] = useState(0);

  const referenceInputRef = useRef<HTMLInputElement>(null);
  const garmentInputRef = useRef<HTMLInputElement>(null);

  const styles = [
    { name: "时尚现代", icon: "✨", prompt: "modern fashion style, contemporary design" },
    { name: "复古经典", icon: "🎭", prompt: "vintage classic style, retro fashion" },
    { name: "街头潮流", icon: "🔥", prompt: "streetwear style, urban fashion" },
    { name: "优雅正装", icon: "👔", prompt: "elegant formal wear, sophisticated style" },
    { name: "休闲舒适", icon: "👕", prompt: "casual comfortable style, relaxed fashion" },
    { name: "运动活力", icon: "⚡", prompt: "sporty active style, athletic wear" }
  ];

  const ratios = ["1:1", "16:9", "4:3", "3:2", "2:3", "3:4", "9:16"];
  const counts = ["1", "2", "4"];



  const buildPrompt = (userPrompt: string) => {
    const stylePrompt = styles.find(s => s.name === selectedStyle)?.prompt || "";
    return `${userPrompt}, ${stylePrompt}, high quality, detailed, professional photography`;
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);

    // 暂时使用模拟延迟，专注于页面集成效果
    // 后续阶段4会按照项目规范集成真实API
    setTimeout(() => {
      setIsGenerating(false);
      // 模拟成功生成的反馈
      console.log('模拟生成完成:', {
        prompt: buildPrompt(prompt),
        style: selectedStyle,
        ratio: selectedRatio,
        count: selectedCount
      });
    }, 3000);
  };

  return (
    <div className="mx-auto max-w-4xl">
      <Card className="border-2 border-primary/20 bg-gradient-to-br from-background to-muted/30 shadow-xl">
        <CardContent className="p-6">
          <Tabs defaultValue="text-to-image" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="text-to-image" className="flex items-center gap-2">
                <Wand2 className="h-4 w-4" />
                文生图
              </TabsTrigger>
              <TabsTrigger value="image-to-image" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                图生图
                <Badge variant="secondary" className="ml-1 text-xs">热门</Badge>
              </TabsTrigger>
              <TabsTrigger value="style-transfer" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                风格转换
              </TabsTrigger>
            </TabsList>

            <TabsContent value="text-to-image" className="space-y-6">
              <div className="space-y-4">
                <Textarea
                  placeholder="描述您想要的时尚设计，例如：一件优雅的黑色晚礼服，带有金色刺绣细节，适合正式场合..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[120px] resize-none text-base"
                />
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* 风格选择 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium flex items-center gap-2">
                      <Shirt className="h-4 w-4" />
                      时尚风格
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {styles.map((style) => (
                        <Button
                          key={style.name}
                          variant={selectedStyle === style.name ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedStyle(style.name)}
                          className="justify-start text-xs"
                        >
                          <span className="mr-1">{style.icon}</span>
                          {style.name}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* 图片比例 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">图片比例</label>
                    <div className="grid grid-cols-3 gap-2">
                      {ratios.map((ratio) => (
                        <Button
                          key={ratio}
                          variant={selectedRatio === ratio ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedRatio(ratio)}
                          className="text-xs"
                        >
                          {ratio}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* 生成数量 */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">生成数量</label>
                    <div className="grid grid-cols-3 gap-2">
                      {counts.map((count) => (
                        <Button
                          key={count}
                          variant={selectedCount === count ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedCount(count)}
                          className="text-xs"
                        >
                          {count}张
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setPrompt("")}
                >
                  清除
                </Button>
                <Button
                  className="flex-1 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      开始生成
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="image-to-image" className="space-y-6">
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-2">点击上传或拖拽图片到此处</p>
                <p className="text-sm text-muted-foreground">支持 JPG、PNG 格式，最大 10MB</p>
              </div>
              
              <Textarea
                placeholder="描述您想要对图片进行的修改，例如：将这件衣服改成红色，添加蕾丝装饰..."
                className="min-h-[80px] resize-none"
              />
              
              <Button className="w-full" disabled>
                <Upload className="h-4 w-4 mr-2" />
                上传图片后开始生成
              </Button>
            </TabsContent>

            <TabsContent value="style-transfer" className="space-y-6">
              <div className="text-center py-8">
                <Palette className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">风格转换功能</h3>
                <p className="text-muted-foreground">即将推出，敬请期待</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import { useState, useRef } from "react";

export default function AIFashionTool() {
  const [referenceImage, setReferenceImage] = useState<string | null>(null);
  const [garmentImage, setGarmentImage] = useState<string | null>(null);
  const [styleIntensity, setStyleIntensity] = useState(75);
  const [quality, setQuality] = useState("high");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [selectedHistoryItem, setSelectedHistoryItem] = useState(0);
  
  const referenceInputRef = useRef<HTMLInputElement>(null);
  const garmentInputRef = useRef<HTMLInputElement>(null);

  // 历史记录数据
  const historyItems = [
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=400&fit=crop&crop=face",
      title: "Elegant Evening Dress",
      time: "2 minutes ago"
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop&crop=face",
      title: "Casual Streetwear", 
      time: "15 minutes ago"
    },
    {
      id: 3,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
      title: "Business Formal",
      time: "1 hour ago"
    },
    {
      id: 4,
      image: "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=400&fit=crop&crop=face",
      title: "Summer Collection",
      time: "2 hours ago"
    }
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, type: 'reference' | 'garment') => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (type === 'reference') {
          setReferenceImage(result);
        } else {
          setGarmentImage(result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerate = async () => {
    if (!referenceImage || !garmentImage) return;
    
    setIsGenerating(true);
    setProgress(0);
    
    // 模拟进度
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsGenerating(false);
          setGeneratedImage("https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=1024&h=576&fit=crop&crop=face");
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
  };

  const handleHistoryClick = (index: number) => {
    setSelectedHistoryItem(index);
    setGeneratedImage(historyItems[index].image.replace('400x400', '1024x576'));
  };

  return (
    <div className="ai-fashion-studio">
      <style jsx>{`
        .ai-fashion-studio {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #0A0A0A;
          color: #FFFFFF;
          overflow-x: hidden;
          min-height: 100vh;
        }

        .ai-fashion-studio .upload-zone {
          border: 2px dashed #333333;
          transition: all 0.3s ease;
        }

        .ai-fashion-studio .upload-zone:hover {
          border-color: #0066FF;
          background: rgba(0, 102, 255, 0.05);
        }

        .ai-fashion-studio .upload-zone.dragover {
          border-color: #0066FF;
          background: rgba(0, 102, 255, 0.1);
        }

        .ai-fashion-studio .thumbnail-item {
          transition: all 0.2s ease;
        }

        .ai-fashion-studio .thumbnail-item:hover {
          transform: scale(1.02);
          box-shadow: 0 4px 20px rgba(0, 102, 255, 0.2);
        }

        .ai-fashion-studio .thumbnail-item.selected {
          border: 2px solid #0066FF;
          box-shadow: 0 0 0 4px rgba(0, 102, 255, 0.2);
        }

        .ai-fashion-studio .main-stage {
          background: radial-gradient(circle at center, #1A1A1A 0%, #141414 100%);
        }

        .ai-fashion-studio .control-panel,
        .ai-fashion-studio .history-panel {
          background: linear-gradient(180deg, #1A1A1A 0%, #161616 100%);
        }

        .ai-fashion-studio .generated-image {
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .ai-fashion-studio .progress-bar {
          background: linear-gradient(90deg, #0066FF 0%, #0080FF 100%);
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }

        .ai-fashion-studio .status-indicator {
          animation: blink 1.5s infinite;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0.3; }
        }

        .ai-fashion-studio .scrollbar-thin::-webkit-scrollbar {
          width: 4px;
        }

        .ai-fashion-studio .scrollbar-thin::-webkit-scrollbar-track {
          background: #1A1A1A;
        }

        .ai-fashion-studio .scrollbar-thin::-webkit-scrollbar-thumb {
          background: #333333;
          border-radius: 2px;
        }

        .ai-fashion-studio .scrollbar-thin::-webkit-scrollbar-thumb:hover {
          background: #0066FF;
        }
      `}</style>
      <div className="flex h-screen bg-black text-white">
      {/* Left Column - Control Panel */}
      <div className="w-80 bg-gradient-to-b from-gray-900 to-gray-800 border-r border-gray-700 flex flex-col">
        <div className="p-6 border-b border-gray-700">
          <h1 className="text-xl font-semibold text-white mb-2">AI Fashion Studio</h1>
          <p className="text-sm text-gray-400">Professional Fashion Generation</p>
        </div>
        
        <div className="flex-1 p-6 space-y-6">
          {/* Reference Image Upload */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-300">Reference Image (Model)</label>
            {!referenceImage ? (
              <div 
                className="border-2 border-dashed border-gray-600 hover:border-blue-500 rounded-lg p-6 text-center cursor-pointer transition-all duration-300 hover:bg-blue-500/5"
                onClick={() => referenceInputRef.current?.click()}
              >
                <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center text-gray-500">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-400 mb-2">Drop your reference image here</p>
                <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                <input 
                  type="file" 
                  className="hidden" 
                  ref={referenceInputRef}
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, 'reference')}
                />
              </div>
            ) : (
              <div>
                <img className="w-full h-32 object-cover rounded-lg" src={referenceImage} alt="Reference preview" />
                <button 
                  className="mt-2 text-xs text-red-400 hover:text-red-300"
                  onClick={() => setReferenceImage(null)}
                >
                  Remove
                </button>
              </div>
            )}
          </div>
          
          {/* Garment Image Upload */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-300">Garment Image</label>
            {!garmentImage ? (
              <div 
                className="border-2 border-dashed border-gray-600 hover:border-blue-500 rounded-lg p-6 text-center cursor-pointer transition-all duration-300 hover:bg-blue-500/5"
                onClick={() => garmentInputRef.current?.click()}
              >
                <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center text-gray-500">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-400 mb-2">Drop your garment image here</p>
                <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                <input 
                  type="file" 
                  className="hidden" 
                  ref={garmentInputRef}
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, 'garment')}
                />
              </div>
            ) : (
              <div>
                <img className="w-full h-32 object-cover rounded-lg" src={garmentImage} alt="Garment preview" />
                <button 
                  className="mt-2 text-xs text-red-400 hover:text-red-300"
                  onClick={() => setGarmentImage(null)}
                >
                  Remove
                </button>
              </div>
            )}
          </div>
          
          {/* Generation Settings */}
          <div className="space-y-4 pt-4 border-t border-gray-700">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Style Intensity</label>
              <div className="relative">
                <input 
                  type="range" 
                  min="0" 
                  max="100" 
                  value={styleIntensity} 
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  onChange={(e) => setStyleIntensity(Number(e.target.value))}
                />
                <div 
                  className="absolute -top-8 transform -translate-x-1/2 text-xs text-gray-400"
                  style={{ left: `${styleIntensity}%` }}
                >
                  {styleIntensity}%
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Quality</label>
              <div className="flex space-x-2">
                {['standard', 'high', 'ultra'].map((q) => (
                  <button 
                    key={q}
                    className={`px-3 py-1 text-xs rounded transition-colors ${
                      quality === q 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                    onClick={() => setQuality(q)}
                  >
                    {q.charAt(0).toUpperCase() + q.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          {/* Generate Button */}
          <button 
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-medium py-3 px-4 rounded transition-colors"
            onClick={handleGenerate}
            disabled={!referenceImage || !garmentImage || isGenerating}
          >
            <span className="flex items-center justify-center">
              {isGenerating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                  Generate Fashion
                </>
              )}
            </span>
          </button>
        </div>
      </div>

      {/* Center Column - Main Stage */}
      <div className="flex-1 bg-gradient-to-br from-gray-900 to-gray-800 flex flex-col">
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-medium text-white">Generation Preview</h2>
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <div className={`w-2 h-2 rounded-full ${isGenerating ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'}`}></div>
                <span>{isGenerating ? 'Generating' : 'Ready'}</span>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="p-2 text-gray-400 hover:text-white rounded hover:bg-gray-700 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>
              <button className="p-2 text-gray-400 hover:text-white rounded hover:bg-gray-700 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
              </button>
              <button className="p-2 text-gray-400 hover:text-white rounded hover:bg-gray-700 transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="flex-1 p-6 flex items-center justify-center">
          <div className="w-full max-w-2xl">
            <div className="aspect-[16/9] bg-gray-800 rounded-xl overflow-hidden relative shadow-2xl">
              {!generatedImage && !isGenerating && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-600">
                      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <p className="text-gray-400 mb-2">Upload images to start generating</p>
                    <p className="text-sm text-gray-500">Your AI-generated fashion will appear here</p>
                  </div>
                </div>
              )}

              {generatedImage && !isGenerating && (
                <img className="w-full h-full object-cover" src={generatedImage} alt="Generated fashion" />
              )}

              {isGenerating && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-white mb-2">Generating your fashion...</p>
                    <div className="w-48 h-1 bg-gray-700 rounded-full mx-auto">
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="mt-4 flex items-center justify-between text-sm text-gray-400">
              <div className="flex items-center space-x-4">
                <span>Resolution: 1024 × 576</span>
                <span>Format: PNG</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>Generation time: --</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Column - History Gallery */}
      <div className="w-72 bg-gradient-to-b from-gray-900 to-gray-800 border-l border-gray-700 flex flex-col">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-lg font-medium text-white mb-2">Generation History</h3>
          <p className="text-sm text-gray-400">Recent creations</p>
        </div>

        <div className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-3">
            {historyItems.map((item, index) => (
              <div
                key={item.id}
                className={`bg-gray-800 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg ${
                  selectedHistoryItem === index ? 'ring-2 ring-blue-500 shadow-lg' : ''
                }`}
                onClick={() => handleHistoryClick(index)}
              >
                <div className="aspect-square bg-gray-700 relative">
                  <img src={item.image} className="w-full h-full object-cover object-top" alt="Fashion generation" />
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                </div>
                <div className="p-3">
                  <p className="text-xs text-gray-400 mb-1">{item.time}</p>
                  <p className="text-sm text-gray-300">{item.title}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="p-4 border-t border-gray-700">
          <button className="w-full text-sm text-gray-400 hover:text-white py-2 text-center transition-colors">
            View All History
          </button>
        </div>
      </div>
    </div>
  );
}
